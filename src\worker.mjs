/**
 * Cloudflare Worker entry point that acts as a proxy/adapter between
 * OpenAI-compatible API requests and Google's Gemini API.
 *
 * Provides OpenAI API compatibility for chat completions, embeddings,
 * and model listing while translating requests to Gemini API format.
 */
import {
  handleCompletions,
  handleEmbeddings,
  handleModels,
  handleTTS,
  ScheduledHandler
} from './handlers/index.mjs';

import {
  getRandomApi<PERSON>ey,
  fix<PERSON><PERSON>,
  errorHandler,
  HttpError
} from './utils/index.mjs';

import { handleOPTIONS } from './utils/cors.mjs';

/**
 * Main Cloudflare Worker handler that processes incoming HTTP requests
 * and routes them to appropriate handlers based on the endpoint path.
 *
 * Supports the following OpenAI-compatible endpoints:
 * - POST /chat/completions - Chat completion requests
 * - POST /embeddings - Text embedding requests
 * - GET /models - Available model listing
 * - POST /tts - Text-to-speech requests
 *
 * @param {Request} request - The incoming HTTP request
 * @param {Object} env - Cloudflare Worker environment variables
 * @returns {Promise<Response>} HTTP response with CORS headers applied
 */
async function fetch(request, env) {
  if (request.method === "OPTIONS") {
    return handleOPTIONS();
  }

  const errHandler = (err) => errorHandler(err, fixCors);

  try {
    const apiKey = getRandomApiKey(request, env);

    // Block requests from specific Cloudflare data centers that may have
    // connectivity issues with Google's API endpoints
    const colo = request.cf?.colo;
    if (colo && ["DME", "LED", "SVX", "KJA"].includes(colo)) {
      return new Response(`Bad Cloudflare colo: ${colo}. Try again`, {
        status: 429,
        headers: { "Content-Type": "text/plain" },
      });
    }

    const { pathname } = new URL(request.url);
    switch (true) {
      case pathname.endsWith("/chat/completions"):
        if (!(request.method === "POST")) {
          throw new Error("Assertion failed: expected POST request");
        }
        return handleCompletions(await request.json(), apiKey)
          .catch(errHandler);

      case pathname.endsWith("/embeddings"):
      case pathname.endsWith("/embed"):
        if (!(request.method === "POST")) {
          throw new Error("Assertion failed: expected POST request");
        }
        return handleEmbeddings(await request.json(), apiKey)
          .catch(errHandler);

      case pathname.endsWith("/models"):
        if (!(request.method === "GET")) {
          throw new Error("Assertion failed: expected GET request");
        }
        return handleModels(apiKey)
          .catch(errHandler);

      case pathname.endsWith("/tts"):
        if (!(request.method === "POST")) {
          throw new Error("Assertion failed: expected POST request");
        }
        return handleTTS(request, apiKey)
          .catch(errHandler);

      default:
        throw new HttpError("404 Not Found", 404);
    }
  } catch (err) {
    return errHandler(err);
  }
}

export default {
  fetch,

  /**
   * Handles scheduled tasks
   * @param {ScheduledController} controller - The scheduled controller
   * @param {CloudflareEnv} env - Cloudflare Workers environment
   * @param {ExecutionContext} _ctx - Execution context (unused)
   */
  async scheduled(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _controller,
    env,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _ctx
  ) {
    const handler = new ScheduledHandler(env);
    await handler.handleScheduledTasks();
  }
};
