name = "gemini-openai-adapter"
main = "src/worker.mjs"
compatibility_date = "2025-01-01"
compatibility_flags = [ "nodejs_compat" ]

[placement]
mode = "smart"

# --- Development Environment ---
# Use only 'npx run dev' for debug!

[env.dev.vars]
PASS = "sk-proj-MASKED"
KEY1 = "AIzaSyMASKED"
KEY2 = "AIzaSyMASKED"
DEFAULT_MODEL = "gemini-2.5-flash-preview-05-20"

# --- Production Environment ---
# Use only 'npx run deploy' for deploy!

[env.production]
preview_urls = false
workers_dev = false
routes = [
  { pattern = "gemini-openai-adapter.MASKED.MASKED", custom_domain = true }
]

[env.production.vars]
PASS = "sk-proj-MASKED"
KEY1 = "AIzaSyMASKED"
KEY2 = "AIzaSyMASKED"
KEY3 = "AIzaSyMASKED"
KEY4 = "AIzaSyMASKED"
KEY5 = "AIzaSyMASKED"
KEY6 = "AIzaSyMASKED"
KEY7 = "AIzaSyMASKED"
KEY8 = "AIzaSyMASKED"
KEY9 = "AIzaSyMASKED"
KEY10 = "AIzaSyMASKED"
KEY11 = "AIzaSyMASKED"
KEY12 = "AIzaSyMASKED"
KEY13 = "AIzaSyMASKED"
KEY14 = "AIzaSyMASKED"
KEY15 = "AIzaSyMASKED"
KEY16 = "AIzaSyMASKED"
KEY17 = "AIzaSyMASKED"
KEY18 = "AIzaSyMASKED"
KEY19 = "AIzaSyMASKED"
KEY20 = "AIzaSyMASKED"
DEFAULT_MODEL = "gemini-2.5-flash-preview-05-20"

[observability.logs]
enabled = true

[env.production.triggers]
# run at every minute
crons = [ "* * * * *"]
