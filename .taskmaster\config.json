{"models": {"main": {"provider": "openai", "modelId": "gemini-2.5-flash-preview-05-20-refined-high", "baseUrl": "https://gemini-openai-adapter.100169.xyz/v1", "maxTokens": 200000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar", "maxTokens": 8000, "temperature": 0.1}, "fallback": {"provider": "openai", "modelId": "gemini-2.5-flash-preview-05-20-refined-high", "baseUrl": "https://gemini-openai-adapter.100169.xyz/v1", "maxTokens": 200000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 10, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}