{"name": "openai-gemini-mod", "version": "2.4.0", "type": "module", "dependencies": {"@whatwg-node/server": "0.9"}, "devDependencies": {"@jest/globals": "^29.7.0", "clear-cli": "^2.0.0", "jest": "^29.7.0", "wrangler": "^4.16.1"}, "scripts": {"deploy": "npx clear-cli && wrangler deploy --env production", "test": "npx clear-cli && node --no-warnings --experimental-vm-modules node_modules/jest/bin/jest.js", "test:coverage": "npx clear-cli && node --no-warnings --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "dev": "npx clear-cli && npx wrangler dev --env dev"}}