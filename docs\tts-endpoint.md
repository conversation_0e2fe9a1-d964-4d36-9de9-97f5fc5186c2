**TTS Usage:**
`POST https://<your-worker-domain>/tts?voiceName=<VOICE1>&secondVoiceName=<VOICE2>`
Headers: `Authorization: Bearer <WORKER_ACCESS_PASS>`, `Content-Type: application/json`
Body: `{"text": "string (1-4k chars, <5kB)", "model": "string (e.g., gemini-2.5-flash-preview-tts)"}`

**Voices (`voiceName`):**
*   **Gemini Only**: `Zephyr, Puck, Charon, Kore, Fenrir, Leda, Orus, Aoede, Callirrhoe, Autonoe, Enceladus, Iapetus, Umbriel, Algieba, Despina, Erinome, Algenib, Rasalgethi, Laomedeia, Achernar, Alnilam, Schedar, Gacrux, Pulcherrima, Achird, Zubenelgenubi, Vindemiatrix, Sadachbia, Sadaltager, Sulafat`

**Example cURL:**
```bash
curl -X POST "https://<domain>/tts?voiceName=Puck" \
  -H "Authorization: Bearer <PASS>" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello.", "model": "gemini-2.5-flash-preview-tts"}' \
  --output audio_base64.txt
```

**Base64 to WAV (PCM Audio):**
Response: Base64 audio string. Headers: `X-Sample-Rate` (e.g., 24000), `X-Audio-Format` (e.g., `audio/L16;rate=24000`). Audio is 16-bit PCM, mono.

1.  **Decode Base64**: Convert base64 string to raw binary PCM data bytes.
    *   JS: `const binaryString = atob(base64Audio); const pcmData = new Uint8Array(binaryString.length); for (let i=0; i<binaryString.length; i++) pcmData[i] = binaryString.charCodeAt(i);`

2.  **Generate 44-byte WAV Header**: (All multi-byte integers are Little Endian)
    *   `0-3`: "RIFF" (ASCII `0x52494646`)
    *   `4-7`: ChunkSize (32-bit int) = 36 + `pcmData.length`
    *   `8-11`: "WAVE" (ASCII `0x57415645`)
    *   `12-15`: "fmt " (ASCII `0x666d7420`) (Sub-chunk 1 ID)
    *   `16-19`: Subchunk1Size (32-bit int) = 16 (for PCM)
    *   `20-21`: AudioFormat (16-bit int) = 1 (PCM)
    *   `22-23`: NumChannels (16-bit int) = 1 (mono)
    *   `24-27`: SampleRate (32-bit int) = from `X-Sample-Rate` header (e.g., 24000)
    *   `28-31`: ByteRate (32-bit int) = `SampleRate * NumChannels * BitsPerSample/8` (e.g., `24000 * 1 * 16/8 = 48000`)
    *   `32-33`: BlockAlign (16-bit int) = `NumChannels * BitsPerSample/8` (e.g., `1 * 16/8 = 2`)
    *   `34-35`: BitsPerSample (16-bit int) = 16
    *   `36-39`: "data" (ASCII `0x64617461`) (Sub-chunk 2 ID)
    *   `40-43`: Subchunk2Size (32-bit int) = `pcmData.length`

3.  **Combine**: Prepend the generated 44-byte WAV header to the raw PCM data bytes.
    *   JS: `const wavFile = new Uint8Array(wavHeader.length + pcmData.length); wavFile.set(wavHeader); wavFile.set(pcmData, wavHeader.length);`
