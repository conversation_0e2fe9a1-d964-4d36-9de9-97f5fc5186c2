/**
 * <PERSON><PERSON> for Text-to-Speech (TTS) endpoint.
 * Processes TTS requests and integrates with Google's Generative AI TTS API.
 * Optimized for minimal CPU usage during long API calls.
 */
import { fixCors } from '../utils/cors.mjs';
import {
  error<PERSON><PERSON><PERSON>,
  <PERSON>ttpError,
  processG<PERSON>gle<PERSON><PERSON><PERSON>rror
} from '../utils/error.mjs';
import { makeHeaders } from '../utils/auth.mjs';
import {
  BASE_URL,
  API_VERSION
} from '../constants/index.mjs';

// Hardcoded sample rate for maximum performance (Gemini typically uses 24000Hz)
const DEFAULT_SAMPLE_RATE = 24000;

// Pre-allocated response headers template for memory efficiency
const RESPONSE_HEADERS_TEMPLATE = {
  'Content-Type': 'application/json',
  'X-Audio-Encoding': 'base64'
};

// Pre-computed URL template for faster string concatenation
const API_URL_PREFIX = `${BASE_URL}/${API_VERSION}/models/`;
const API_URL_SUFFIX = ':generateContent';

/**
 * Constructs the request body for Google Generative AI TTS API.
 * Optimized for minimal object creation and CPU overhead.
 *
 * @param {Object} params - Parameters for TTS request
 * @param {string} params.text - Text to synthesize
 * @param {string} params.firstVoiceName - Primary voice name
 * @param {string|null} params.secondVoiceName - Secondary voice name (optional)
 * @returns {Object} Google API request body structure
 */
function constructGoogleTTSRequestBody({ text, firstVoiceName, secondVoiceName }) {
  // Optimized object construction - build final structure directly
  const speechConfig = secondVoiceName ? {
    // Multi-speaker configuration
    multiSpeakerVoiceConfig: {
      speakerVoiceConfigs: [
        {
          speaker: "Speaker1",
          voiceConfig: {
            prebuiltVoiceConfig: { voiceName: firstVoiceName }
          }
        },
        {
          speaker: "Speaker2",
          voiceConfig: {
            prebuiltVoiceConfig: { voiceName: secondVoiceName }
          }
        }
      ]
    }
  } : {
    // Single-speaker configuration
    voiceConfig: {
      prebuiltVoiceConfig: { voiceName: firstVoiceName }
    }
  };

  // Return optimized structure with minimal nesting
  return {
    contents: [{ parts: [{ text }] }],
    generationConfig: {
      responseModalities: ["AUDIO"],
      speechConfig
    }
  };
}

/**
 * Makes a request to Google's Generative AI API for text-to-speech generation.
 * Optimized for minimal CPU usage during response processing.
 *
 * @param {string} model - The Gemini model to use for TTS generation
 * @param {Object} requestBody - The constructed request body for Google API
 * @param {string} apiKey - Google API key for authentication
 * @returns {Promise<Object>} Object containing base64 audio data, mimeType, and sampleRate
 * @throws {HttpError} When API call fails or response is invalid
 */
async function callGoogleTTSAPI(model, requestBody, apiKey) {
  // Optimized URL construction using pre-computed constants
  const url = API_URL_PREFIX + model + API_URL_SUFFIX;

  try {
    // Make the fetch request to Google's API
    const response = await fetch(url, {
      method: 'POST',
      headers: makeHeaders(apiKey, { 'Content-Type': 'application/json' }),
      body: JSON.stringify(requestBody)
    });

    // Handle non-200 responses using enhanced error processing
    if (!response.ok) {
      throw await processGoogleApiError(response);
    }

    // Single-pass JSON parsing with immediate validation and extraction
    const responseData = await response.json();

    // Optimized single-pass validation and extraction
    // Fast path: direct property access with minimal validation
    const candidates = responseData.candidates;
    if (!candidates?.[0]?.content?.parts?.[0]?.inlineData) {
      // Detailed validation only on error path to minimize CPU overhead
      if (!candidates || !Array.isArray(candidates) || candidates.length === 0) {
        throw new HttpError('Invalid response structure: no candidates found', 502);
      }
      const candidate = candidates[0];
      if (!candidate.content?.parts || !Array.isArray(candidate.content.parts) || candidate.content.parts.length === 0) {
        throw new HttpError('Invalid response structure: no content parts found', 502);
      }
      throw new HttpError('Invalid response structure: no inline data found', 502);
    }

    // Direct extraction without intermediate variables
    const inlineData = candidates[0].content.parts[0].inlineData;
    const { data: base64Audio, mimeType } = inlineData;

    // Validate required fields exist
    if (!base64Audio || !mimeType) {
      throw new HttpError('Invalid response structure: missing audio data or mimeType', 502);
    }

    return {
      base64Audio,
      mimeType,
      sampleRate: DEFAULT_SAMPLE_RATE
    };

  } catch (error) {
    // Re-throw HttpErrors as-is
    if (error instanceof HttpError) {
      throw error;
    }

    // Handle network errors and other fetch failures
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new HttpError('Network error: Unable to connect to Google API', 502);
    }

    // Handle other unexpected errors
    throw new HttpError(`Unexpected error during API call: ${error.message}`, 500);
  }
}

/**
 * Processes text-to-speech requests with optimized CPU usage.
 * Minimizes string operations and object creation during validation.
 *
 * @param {Request} request - The incoming HTTP request containing TTS parameters
 * @param {string} apiKey - Google API key for Gemini access
 * @returns {Promise<Response>} HTTP response with audio data or error information
 * @throws {Error} When request validation fails or API call errors
 */
export async function handleTTS(request, apiKey) {
  try {
    // Fast path validation - minimal CPU overhead
    if (!apiKey) {
      throw new HttpError("API key is required", 401);
    }

    // Parse query parameters and request body concurrently for better performance
    const url = new URL(request.url);
    const [voiceName, secondVoiceName, requestBody] = await Promise.all([
      url.searchParams.get('voiceName'),
      url.searchParams.get('secondVoiceName'),
      request.json().catch(() => {
        throw new HttpError("Invalid JSON in request body", 400);
      })
    ]);

    const { text, model } = requestBody;

    // Optimized validation with backward-compatible error messages
    if (!voiceName) {
      throw new HttpError("voiceName query parameter is required", 400);
    }
    if (!text) {
      throw new HttpError("text field is required in request body", 400);
    }
    if (!model) {
      throw new HttpError("model field is required in request body", 400);
    }

    // Construct Google Generative AI TTS request body using pre-trimmed values
    const googleApiRequestBody = constructGoogleTTSRequestBody({
      text: text,
      firstVoiceName: voiceName,
      secondVoiceName: secondVoiceName
    });

    // Call Google Generative AI API to generate audio
    const { base64Audio, mimeType, sampleRate } = await callGoogleTTSAPI(
      model,
      googleApiRequestBody,
      apiKey
    );

    // Optimized response construction using pre-allocated template
    const responseHeaders = {
      ...RESPONSE_HEADERS_TEMPLATE,
      'X-Audio-Format': mimeType,
      'X-Sample-Rate': String(sampleRate) // Faster than toString()
    };

    return new Response(base64Audio, fixCors({
      status: 200,
      headers: responseHeaders
    }));
  } catch (err) {
    // Use centralized error handler for consistent error responses
    return errorHandler(err, fixCors);
  }
}
